#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Stable Training - Clean implementation without complex loss functions
This addresses high fluctuating loss by using the simplest possible approach.
"""

import torch
import torch.nn.functional as F
import torch.nn.utils as utils
import numpy as np
import time
import os
from torch.utils.tensorboard import SummaryWriter
from torch.optim.lr_scheduler import ReduceLROnPlateau

# Import your existing modules
from multi_robot_system import MultiRobotDecentralizedSystem
from utils import build_hetgraph, hetgraph_node_helper
from multi_objective_utils import MultiObjectiveMetrics

def simple_train_system(robot_memories, num_robots, args):
    """Ultra-simple training without complex loss functions."""
    
    device = torch.device(args.device)
    
    # Network setup (same as before)
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'),
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'),
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'),
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'),
        ('state', 'sin', 'state'), ('task', 'tto', 'value'),
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'),
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'),
        ('robot', 'use_time', 'task')
    ]

    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 8).to(device)
    optimizers = [torch.optim.Adam(system.get_robot_network(i).parameters(), 
                                  lr=args.lr, weight_decay=args.weight_decay) for i in range(num_robots)]
    
    # Simple reward normalization
    all_rewards = []
    for robot_memory in robot_memories:
        for transition in robot_memory:
            all_rewards.append(float(transition.reward_n))
    
    if all_rewards:
        reward_mean = np.mean(all_rewards)
        reward_std = np.std(all_rewards) + 1e-8
        print(f"Reward stats: mean={reward_mean:.3f}, std={reward_std:.3f}")
    else:
        reward_mean, reward_std = 0.0, 1.0

    writer = SummaryWriter(log_dir=args.tbdir)
    metrics = MultiObjectiveMetrics()
    
    print(f"🚀 SIMPLE STABLE TRAINING - {args.steps} steps")
    print(f"📊 Focus: Stable loss decrease, effective learning")
    
    for step in range(1, args.steps + 1):
        start_time = time.time()
        total_loss = 0.0
        step_rewards = []
        
        for robot_id in range(num_robots):
            if len(robot_memories[robot_id]) == 0:
                continue
                
            # Simple batch sampling
            batch_size = min(args.batch_size, len(robot_memories[robot_id]))
            indices = np.random.choice(len(robot_memories[robot_id]), batch_size, replace=False)
            batch = [robot_memories[robot_id][i] for i in indices]
            
            robot_loss = torch.tensor(0.0, device=device, requires_grad=True)
            
            for transition in batch:
                unscheduled = [tid for tid in range(1, transition.durs.shape[0]+1) 
                             if tid not in transition.curr_partialw]
                if not unscheduled:
                    continue
                
                # Build graph
                g = build_hetgraph(
                    transition.curr_g, transition.durs.shape[0], num_robots,
                    transition.durs.astype(np.float32), 6,
                    np.array(transition.locs, dtype=np.int64), 1,
                    transition.curr_partials, np.array(unscheduled, dtype=np.int32),
                    robot_id, np.array(unscheduled, dtype=np.int32)
                ).to(device)
                
                # Build features
                feat = hetgraph_node_helper(
                    g.number_of_nodes(), transition.curr_partialw, transition.curr_partials,
                    transition.locs, transition.durs, 6, num_robots, len(unscheduled)
                )
                feat = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat.items()}
                
                # Forward pass - NO communication rounds to reduce complexity
                out = system.forward_with_communication(robot_id, g, feat, communication_rounds=1)
                q, conf = out['value'], out['confidence']
                
                # SIMPLE reward processing - no aggressive scaling
                raw_reward = float(transition.reward_n)
                normalized_reward = (raw_reward - reward_mean) / reward_std
                normalized_reward = np.clip(normalized_reward, -1.0, 1.0)  # Reasonable range
                
                # SIMPLE targets - no complex weighting
                targets = torch.full((len(unscheduled), 1), normalized_reward, device=device)
                if transition.act_task in unscheduled:
                    idx = unscheduled.index(transition.act_task)
                    targets[idx] = normalized_reward  # Same target for simplicity
                
                # SIMPLE loss - just MSE, no complex terms
                mse_loss = F.mse_loss(q, targets, reduction='mean')
                
                # SIMPLE confidence regularization
                conf_reg = torch.mean((conf - 0.5)**2) * 0.01  # Very small
                
                # SIMPLE total loss - no balance, makespan, or other complex terms
                simple_loss = mse_loss + conf_reg
                robot_loss = robot_loss + simple_loss
                
                step_rewards.append(raw_reward)
            
            # Simple optimization
            optimizers[robot_id].zero_grad()
            if robot_loss.requires_grad and not torch.isnan(robot_loss):
                robot_loss.backward()
                # Conservative gradient clipping
                utils.clip_grad_norm_(system.get_robot_network(robot_id).parameters(), max_norm=0.5)
                optimizers[robot_id].step()
                total_loss += robot_loss.item()
        
        # Simple metrics
        avg_reward = np.mean(step_rewards) if step_rewards else 0.0
        
        # Simple logging
        writer.add_scalar('Loss/Total', total_loss, step)
        writer.add_scalar('Reward/Avg', avg_reward, step)
        
        # Simple progress reporting
        if step % 5 == 0 or step <= 10:
            print(f"[Step {step:3d}] SIMPLE TRAINING - Loss: {total_loss:.4f}, "
                  f"AvgReward: {avg_reward:.4f}, Time: {time.time()-start_time:.2f}s")
        
        # Early stopping if loss is reasonable
        if total_loss < 1.0:
            print(f"✅ Loss below 1.0 achieved at step {step}!")
    
    writer.close()
    print("✅ Simple training complete.")
    return system

def main():
    import argparse
    from decentralized_multi_objective_train import collect_decentralized_data
    
    parser = argparse.ArgumentParser(description="Simple stable training")
    parser.add_argument("--path-to-train", default="./problem_instances/constraints")
    parser.add_argument("--num-robots", type=int, default=2)
    parser.add_argument("--train-start-no", type=int, default=1)
    parser.add_argument("--train-end-no", type=int, default=30)
    parser.add_argument("--steps", type=int, default=50)  # Short test
    parser.add_argument("--batch-size", type=int, default=4)  # Small batches
    parser.add_argument("--lr", type=float, default=1e-3)  # Higher LR for simple loss
    parser.add_argument("--weight-decay", type=float, default=1e-4)  # Regularization
    parser.add_argument("--alpha", type=float, default=0.5)  # Not used in simple training
    parser.add_argument("--beta", type=float, default=0.5)   # Not used in simple training
    parser.add_argument("--cpsave", default="./cp_simple")
    parser.add_argument("--tbdir", default="./runs/SimpleStable")
    parser.add_argument("--device", default="cpu")
    args = parser.parse_args()
    
    print("🧹 SIMPLE STABLE TRAINING")
    print("=" * 40)
    print("🎯 Goal: Stable, low loss without complexity")
    print("🔧 Approach: Minimal loss function, no complex terms")
    print("📊 Expected: Loss 0.1-2.0, no fluctuations")
    print()
    
    # Collect data
    robot_memories = collect_decentralized_data(
        args.path_to_train, args.train_start_no, args.train_end_no,
        args.num_robots, args.alpha, args.beta
    )
    
    if sum(len(m) for m in robot_memories) == 0:
        print("❌ No training data found!")
        return
    
    print(f"✅ Loaded {sum(len(m) for m in robot_memories)} transitions")
    
    # Run simple training
    system = simple_train_system(robot_memories, args.num_robots, args)
    
    print(f"\n📈 Check results: tensorboard --logdir {args.tbdir}")
    print(f"🎯 If loss is stable and low, we can add back makespan/balance terms gradually")

if __name__ == "__main__":
    main()
