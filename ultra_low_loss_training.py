#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra-Low Loss Training Script for Multi-Robot Scheduling
Target: Achieve loss values below 0.1 with gradual convergence

This script implements advanced techniques for ultra-precise training:
- Multi-stage reward normalization
- Progressive learning rate scheduling  
- Loss smoothing and scaling
- Ultra-conservative regularization
- Detailed convergence monitoring
"""

import os
import sys
import subprocess
import argparse
import time
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def run_ultra_low_loss_training(args):
    """Run training with ultra-low loss targeting (<0.1)."""
    
    print("🎯 Ultra-Low Loss Training (<0.1)")
    print("=" * 50)
    
    # Prepare training command with ultra-low loss parameters
    cmd = [
        "python", "decentralized_multi_objective_train.py",
        "--path-to-train", args.data_path,
        "--num-robots", str(args.num_robots),
        "--train-start-no", str(args.start_no),
        "--train-end-no", str(args.end_no),
        "--steps", str(args.steps),
        "--batch-size", str(args.batch_size),
        "--lr", str(args.lr),
        "--weight-decay", str(args.weight_decay),
        "--alpha", str(args.alpha),
        "--beta", str(args.beta),
        "--checkpoint-interval", str(args.checkpoint_interval),
        "--cpsave", args.checkpoint_dir,
        "--tbdir", args.tensorboard_dir,
        "--device", args.device
    ]
    
    print(f"Training command: {' '.join(cmd)}")
    print(f"🎯 Target: Loss < 0.1 with gradual convergence")
    print(f"Advanced techniques enabled:")
    print(f"  ✅ Multi-stage reward normalization (IQR + sigmoid)")
    print(f"  ✅ Progressive learning rate scheduling (warmup + cosine)")
    print(f"  ✅ Ultra-conservative gradient clipping (0.1)")
    print(f"  ✅ Loss scaling and smoothing (EMA)")
    print(f"  ✅ Minimal regularization weights")
    print(f"  ✅ Ultra-small target values (0.01 scale)")
    print()
    
    # Run training with monitoring
    start_time = time.time()
    try:
        # Use Popen for real-time output monitoring
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, bufsize=1, universal_newlines=True)
        
        loss_values = []
        convergence_achieved = False
        
        print("📊 Real-time Loss Monitoring:")
        print("-" * 40)
        
        for line in process.stdout:
            print(line.rstrip())
            
            # Extract loss values for convergence monitoring
            if "Loss:" in line and "Step" in line:
                try:
                    # Parse loss value from output
                    parts = line.split("Loss:")
                    if len(parts) > 1:
                        loss_str = parts[1].split()[0]
                        loss_val = float(loss_str)
                        loss_values.append(loss_val)
                        
                        # Check for ultra-low loss achievement
                        if loss_val < 0.1:
                            if not convergence_achieved:
                                print(f"🎉 MILESTONE: Loss below 0.1 achieved! ({loss_val:.6f})")
                                convergence_achieved = True
                        
                        # Check for gradual convergence
                        if len(loss_values) >= 10:
                            recent_trend = np.mean(loss_values[-5:]) - np.mean(loss_values[-10:-5])
                            if recent_trend < -0.01:
                                print(f"📈 CONVERGENCE: Gradual improvement detected (trend: {recent_trend:.6f})")
                                
                except (ValueError, IndexError):
                    pass
        
        process.wait()
        
        if process.returncode == 0:
            training_time = time.time() - start_time
            print("\n" + "=" * 50)
            print("✅ Ultra-Low Loss Training Completed!")
            print(f"Training time: {training_time:.2f} seconds")
            
            if loss_values:
                final_loss = loss_values[-1]
                min_loss = min(loss_values)
                print(f"Final loss: {final_loss:.6f}")
                print(f"Minimum loss achieved: {min_loss:.6f}")
                
                if min_loss < 0.1:
                    print("🎯 SUCCESS: Target loss (<0.1) achieved!")
                else:
                    print(f"⚠️  Target not reached. Best: {min_loss:.6f} (Target: <0.1)")
                    
                # Analyze convergence
                if len(loss_values) >= 20:
                    early_avg = np.mean(loss_values[:10])
                    late_avg = np.mean(loss_values[-10:])
                    improvement = early_avg - late_avg
                    print(f"Overall improvement: {improvement:.6f}")
                    
                    if improvement > 0:
                        print("📈 CONVERGENCE: Gradual learning confirmed!")
                    else:
                        print("📊 STABLE: Training reached stability")
            
            return True
            
        else:
            print("❌ Training failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during training: {e}")
        return False

def analyze_ultra_low_loss_results(tensorboard_dir):
    """Analyze results specifically for ultra-low loss achievement."""
    
    print("\n🔬 Ultra-Low Loss Analysis")
    print("=" * 30)
    
    tb_path = Path(tensorboard_dir)
    if not tb_path.exists():
        print(f"❌ TensorBoard directory not found: {tensorboard_dir}")
        return
    
    event_files = list(tb_path.glob("**/events.out.tfevents.*"))
    if not event_files:
        print(f"❌ No TensorBoard event files found")
        return
    
    print(f"✅ Found {len(event_files)} TensorBoard event files")
    print(f"📊 Key metrics to monitor:")
    print(f"   • Loss/Total - Overall loss progression")
    print(f"   • Loss/Smoothed - Smoothed loss trend")
    print(f"   • Loss/Target_Below_0.1 - Success rate")
    print(f"   • LossComponents/* - Individual component analysis")
    print(f"   • Training/LearningRate - LR scheduling")
    print(f"   • Gradients/Robot_*_GradNorm - Gradient stability")
    
    print(f"\n🔍 To analyze detailed results:")
    print(f"   tensorboard --logdir {tensorboard_dir}")
    print(f"   Then open http://localhost:6006")
    
    # Check for training curves
    curves_path = tb_path / "training_curves.png"
    if curves_path.exists():
        print(f"✅ Training curves: {curves_path}")

def main():
    parser = argparse.ArgumentParser(description="Ultra-Low Loss Training (<0.1)")
    
    # Data parameters
    parser.add_argument("--data-path", default="./problem_instances/constraints",
                       help="Path to training data")
    parser.add_argument("--num-robots", type=int, default=2,
                       help="Number of robots")
    parser.add_argument("--start-no", type=int, default=1,
                       help="Start instance number")
    parser.add_argument("--end-no", type=int, default=30,
                       help="End instance number")
    
    # Ultra-low loss training parameters
    parser.add_argument("--steps", type=int, default=200,
                       help="Training steps (reduced for testing)")
    parser.add_argument("--batch-size", type=int, default=4,
                       help="Ultra-small batch size for stability")
    parser.add_argument("--lr", type=float, default=5e-5,
                       help="Conservative learning rate")
    parser.add_argument("--weight-decay", type=float, default=1e-6,
                       help="Minimal weight decay")
    parser.add_argument("--alpha", type=float, default=0.3,
                       help="Conservative makespan weight")
    parser.add_argument("--beta", type=float, default=0.7,
                       help="Balance weight")
    
    # System parameters
    parser.add_argument("--checkpoint-interval", type=int, default=50,
                       help="Checkpoint interval")
    parser.add_argument("--checkpoint-dir", default="./cp_ultra_low_loss",
                       help="Checkpoint directory")
    parser.add_argument("--tensorboard-dir", default="./runs/UltraLowLoss",
                       help="TensorBoard directory")
    parser.add_argument("--device", default="cpu",
                       help="Device to use")
    
    args = parser.parse_args()
    
    # Create directories
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.tensorboard_dir, exist_ok=True)
    
    # Run ultra-low loss training
    success = run_ultra_low_loss_training(args)
    
    if success:
        analyze_ultra_low_loss_results(args.tensorboard_dir)
        
        print("\n🎉 Ultra-Low Loss Training Summary")
        print("=" * 40)
        print("Key improvements for sub-0.1 loss:")
        print("  1. ✅ Multi-stage reward normalization (IQR + sigmoid)")
        print("  2. ✅ Progressive LR scheduling (warmup + cosine decay)")
        print("  3. ✅ Ultra-conservative gradient clipping (0.1)")
        print("  4. ✅ Loss scaling and EMA smoothing")
        print("  5. ✅ Minimal regularization to prevent interference")
        print("  6. ✅ Ultra-small target values (0.01 scale)")
        print("  7. ✅ Real-time convergence monitoring")
        
        print(f"\n📈 Monitor detailed progress:")
        print(f"   tensorboard --logdir {args.tensorboard_dir}")
        
    else:
        print("\n❌ Ultra-low loss training failed.")
        print("Troubleshooting tips:")
        print("  • Reduce learning rate further (--lr 1e-5)")
        print("  • Increase training steps (--steps 500)")
        print("  • Check data quality and reward distribution")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
