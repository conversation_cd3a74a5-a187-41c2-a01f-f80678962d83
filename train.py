#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decentralized Multi-Objective Training with:
✅ Safe feature extraction (IndexError caught)
✅ Stronger load balance & makespan penalties
✅ Lower learning rate for stability
✅ Longer default training (1000 steps)
✅ Progress bar via tqdm
✅ TensorBoard logging
✅ Auto Pareto plot on finish
"""

import os
import argparse
import time
import copy
import numpy as np
import torch
import torch.nn.functional as F
import torch.nn.utils as utils
from torch.optim.lr_scheduler import ReduceLROnPlateau
import matplotlib.pyplot as plt
from tqdm import trange

from torch.utils.tensorboard import SummaryWriter
from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper, Transition
from hetnet import MultiRobotDecentralizedSystem
from multi_objective_utils import calculate_workload_balance_from_env, MultiObjectiveMetrics


def collect_decentralized_data(data_dir, start_no, end_no, num_robots, alpha, beta):
    robot_memories = [[] for _ in range(num_robots)]
    for inst_no in range(start_no, end_no + 1):
        fname = os.path.join(data_dir, f"{inst_no:05d}")
        if not os.path.isfile(f"{fname}_dur.txt"):
            continue
        try:
            env = SchedulingEnv(fname)
            env.set_multi_objective_params(alpha=alpha, beta=beta)

            sol_dir = data_dir.replace("/constraints", "/solutions")
            prefix = os.path.join(sol_dir, f"{inst_no:05d}")
            optimals = []
            for r in range(num_robots):
                f_opt = f"{prefix}_{r}.txt"
                if os.path.isfile(f_opt):
                    optimals.append(np.loadtxt(f_opt, dtype=np.int32))
            f_w = f"{prefix}_w.txt"
            if not os.path.isfile(f_w):
                continue
            optimalw = np.loadtxt(f_w, dtype=np.int32)

            states = [[] for _ in range(num_robots)]
            actions = [[] for _ in range(num_robots)]
            rewards = [[] for _ in range(num_robots)]

            for t in range(env.num_tasks):
                task = optimalw[t]
                robot = next((r for r in range(num_robots)
                              if r < len(optimals) and task in optimals[r]), 0)
                states[robot].append({
                    'graph': copy.deepcopy(env.halfDG),
                    'partials': copy.deepcopy(env.partials),
                    'partialw': copy.deepcopy(env.partialw),
                    'locs': env.loc,
                    'durs': env.dur
                })
                ok, rew, _ = env.insert_robot(task, robot)
                if not ok:
                    break
                actions[robot].append(task)
                rewards[robot].append(rew)
                states[robot].append({
                    'graph': copy.deepcopy(env.halfDG),
                    'partials': copy.deepcopy(env.partials),
                    'partialw': copy.deepcopy(env.partialw),
                    'locs': env.loc,
                    'durs': env.dur
                })

            for r in range(num_robots):
                for j in range(len(actions[r])):
                    if j < len(states[r]) - 1:
                        robot_memories[r].append(Transition(
                            curr_g=states[r][j]['graph'],
                            curr_partials=states[r][j]['partials'],
                            curr_partialw=states[r][j]['partialw'],
                            locs=states[r][j]['locs'],
                            durs=states[r][j]['durs'],
                            act_task=actions[r][j],
                            act_robot=r,
                            reward_n=rewards[r][j],
                            next_g=states[r][j+1]['graph'],
                            next_partials=states[r][j+1]['partials'],
                            next_partialw=states[r][j+1]['partialw'],
                            next_done=(j == len(actions[r]) - 1)
                        ))
            print(f"Collected data from instance {inst_no:05d}")
        except Exception as e:
            print(f"Error at instance {inst_no:05d}: {e}")

    total = sum(len(m) for m in robot_memories)
    print(f"Total transitions collected: {total}")
    for i, m in enumerate(robot_memories):
        print(f"  Robot {i}: {len(m)} transitions")
    return robot_memories


def _estimate_makespan_from_transition(tr, device):
    try:
        times = []
        for rid, part in enumerate(tr.curr_partials):
            tasks = [t for t in part if t != 0]
            if tasks:
                times.append(sum(tr.durs[t-1, rid] for t in tasks if t-1 < tr.durs.shape[0]))
            else:
                times.append(0.0)
        return torch.tensor(max(times) if times else 0.0, device=device)
    except:
        return torch.tensor(15.0, device=device)


def _calculate_workload_balance_loss(tr, rid, probs, num_robots, device):
    try:
        loads = torch.zeros(num_robots, device=device)
        for r in range(num_robots):
            loads[r] = len([t for t in tr.curr_partials[r] if t != 0])
        loads[rid] += probs.sum() * 0.1
        std = torch.std(loads, unbiased=False)
        var_pen = std**2 * 0.5
        fair_pen = torch.clamp(torch.max(loads) - torch.min(loads) - 1.0, min=0.0) * 0.5
        return var_pen + fair_pen, std.item()
    except:
        return torch.tensor(0.5, device=device), 1.0


def _validate_current_model(system, args, num_robots, device, _step):
    try:
        start = args.train_end_no + 1
        end = start + 20
        mss, bals, succs = [], [], []
        for inst in range(start, end):
            fname = os.path.join(args.path_to_train.replace("/constraints", "/constraints"),
                                 f"{inst:05d}")
            if not os.path.isfile(f"{fname}_dur.txt"):
                continue
            try:
                env = SchedulingEnv(fname)
                env.set_multi_objective_params(alpha=args.alpha, beta=args.beta)
                ok = True
                for _ in range(env.num_tasks):
                    uns = [t for t in range(1, env.num_tasks+1) if t not in env.partialw]
                    if not uns:
                        break
                    br, bt = 0, uns[0]
                    for r in range(num_robots):
                        if not uns:
                            break
                        g = build_hetgraph(
                            env.halfDG, env.num_tasks, num_robots,
                            env.dur.astype(np.float32), 6,
                            np.array(env.loc), 1,
                            env.partials, np.array(uns),
                            r, np.array(uns)
                        ).to(device)
                        try:
                            feat = hetgraph_node_helper(
                                g.number_of_nodes(),
                                env.partialw, env.partials,
                                env.loc, env.dur,
                                6, num_robots, len(uns)
                            )
                            feat = {k: torch.tensor(v, device=device) for k,v in feat.items()}
                        except IndexError:
                            ok = False
                            break
                        with torch.no_grad():
                            out = system.forward_with_communication(r, g, feat, communication_rounds=2)
                        qv = out['value']
                        if qv.numel() > 0:
                            idx = torch.argmax(qv).item()
                            if idx < len(uns):
                                br, bt = r, uns[idx]
                                break
                    ok, _, _ = env.insert_robot(bt, br)
                    if not ok:
                        break
                if ok:
                    cons, ms = env.check_consistency_makespan(updateDG=False)
                    if cons:
                        mss.append(ms)
                        bals.append(calculate_workload_balance_from_env(env))
                        succs.append(1.0)
                    else:
                        succs.append(0.0)
                else:
                    succs.append(0.0)
            except:
                succs.append(0.0)
        if mss:
            return np.mean(mss), np.mean(bals), np.mean(succs)
        return None
    except:
        return None


def _prioritize_experiences(robot_memories, num_robots):
    pr = [[] for _ in range(num_robots)]
    for r in range(num_robots):
        exps = robot_memories[r]
        if not exps:
            continue
        scored = [(e, _calculate_experience_priority(e)) for e in exps]
        scored.sort(key=lambda x: x[1], reverse=True)
        keep = max(len(scored)//2, min(len(scored), 100))
        pr[r] = [e for e,_ in scored[:keep]]
    return pr


def _calculate_experience_priority(exp):
    try:
        rs = abs(float(exp.reward_n)) * 0.4
        cnt = [len([t for t in p if t != 0]) for p in exp.curr_partials]
        vs = max(0, 2.0 - np.var(cnt)) * 0.3 if cnt else 0
        comp = len(exp.curr_partialw) / (exp.durs.shape[0] + 1)
        cs = comp * 0.3
        return max(0.1, rs + vs + cs)
    except:
        return 0.5


def _plot_training_curves(hist, save_dir):
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Training Progress', fontsize=16, fontweight='bold')
    steps = hist['steps']

    axes[0,0].plot(steps, hist['losses'],   linewidth=2); axes[0,0].set_yscale('log'); axes[0,0].set_title('Loss'); axes[0,0].grid(True)
    axes[0,1].plot(steps, hist['rewards'],  linewidth=2); axes[0,1].set_title('Avg Reward'); axes[0,1].grid(True)
    axes[0,2].plot(steps, hist['makespans'],linewidth=2); axes[0,2].axhline(10.0, linestyle='--'); axes[0,2].set_title('Makespan'); axes[0,2].grid(True)
    axes[1,0].plot(steps, hist['balances'], linewidth=2); axes[1,0].axhline(0.5, linestyle='--'); axes[1,0].set_title('Balance'); axes[1,0].grid(True)
    axes[1,1].plot(steps, [v*100 for v in hist['violation_rates']], linewidth=2); axes[1,1].set_title('Violation %'); axes[1,1].grid(True)
    axes[1,2].plot(steps, hist['phases'],   linewidth=2); axes[1,2].set_title('Phase'); axes[1,2].set_yticks([0,1,2]); axes[1,2].grid(True)

    os.makedirs(save_dir, exist_ok=True)
    plt.tight_layout(); plt.savefig(os.path.join(save_dir, "training_curves.png"), dpi=300); plt.close()


def _plot_training_summary(hist, save_dir):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('Training Summary', fontsize=14, fontweight='bold')
    steps = hist['steps']

    ax1.plot(steps, hist['makespans'], label='Makespan')
    ax1.axhline(10.0, linestyle='--', alpha=0.7)
    ax1.set_xlabel('Step'); ax1.set_ylabel('Makespan'); ax1.grid(True)

    ax2 = ax1.twinx()
    ax2.plot(steps, hist['balances'], label='Balance', linestyle=':')
    ax2.set_ylabel('Balance'); ax2.grid(True)

    ax3 = plt.subplot(1,2,2)
    ax3.plot(steps, hist['violation_rates'], linestyle='-.')
    ax3.set_title('Violation Rate'); ax3.set_xlabel('Step'); ax3.set_ylabel('Rate'); ax3.grid(True)

    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, "training_summary.png"), dpi=300)
    plt.close()


def train_decentralized_system(memories, num_robots, args):
    device = torch.device(args.device)
    in_dim  = {'task':6,'loc':1,'robot':1,'state':4,'value':1}
    hid_dim = {'task':64,'loc':64,'robot':64,'state':64,'value':64}
    out_dim = {'task':32,'loc':32,'robot':32,'state':32,'value':1}
    cetypes = [
        ('task','temporal','task'),('task','located_in','loc'),
        ('loc','near','loc'),('task','assigned_to','robot'),
        ('robot','com','robot'),('task','tin','state'),
        ('loc','lin','state'),('robot','rin','state'),
        ('state','sin','state'),('task','tto','value'),
        ('robot','rto','value'),('state','sto','value'),
        ('value','vto','value'),('task','take_time','robot'),
        ('robot','use_time','task')
    ]

    system = MultiRobotDecentralizedSystem(in_dim, hid_dim, out_dim, cetypes, num_robots, 8).to(device)
    optimizers = [
        torch.optim.Adam(system.get_robot_network(i).parameters(),
                         lr=args.lr, weight_decay=args.weight_decay)
        for i in range(num_robots)
    ]
    schedulers = [
        ReduceLROnPlateau(opt, 'min', factor=0.7, patience=50, min_lr=1e-8)
        for opt in optimizers
    ]

    lengths = [len(m) for m in memories]
    total  = sum(lengths)
    weights = [(total/(l+1e-6))**1.5 for l in lengths]
    sw      = sum(weights)
    weights = [w/sw for w in weights]

    prioritized = _prioritize_experiences(memories, num_robots)
    print(f"Prioritized {sum(len(p) for p in prioritized)} high-quality experiences")

    all_r = [float(t.reward_n) for m in memories for t in m]
    rmin, rmax = (min(all_r), max(all_r)) if all_r else (0.0,1.0)

    print(f"Starting training for {args.steps} steps")
    writer = SummaryWriter(log_dir=args.tbdir)
    metrics = MultiObjectiveMetrics()

    makespan_target = 10.0
    history = {k:[] for k in ['steps','losses','rewards','makespans','balances','violation_rates','phases']}
    phases = 3
    phase_len = args.steps // phases

    def curriculum(s):
        ph = min(s//phase_len, phases-1)
        tgt = makespan_target + 5.0*(phases-1-ph)
        pw  = 2.0*(0.5+0.5*ph/(phases-1))
        bw  = 1.0+2.0*ph/(phases-1)
        return tgt, pw, bw

    for step in trange(1, args.steps+1, desc="Training"):
        start_time = time.time()
        total_loss, batch_rs = 0.0, []
        loads = [0]*num_robots
        ms_list, bal_list = [], []

        tgt, pw, bw = curriculum(step)
        phase = min(step//phase_len, phases-1)

        for rid in range(num_robots):
            mem = memories[rid]
            if not mem:
                continue
            net = system.get_robot_network(rid); net.train()

            bs = min(args.batch_size, len(mem))
            absz = min(max(1, int(bs*weights[rid]*num_robots)), len(mem))
            use_p = (np.random.rand()<0.7 and prioritized[rid])
            src = prioritized[rid] if use_p else mem
            idxs = np.random.choice(len(src), absz, replace=(absz>len(src)))
            batch = [src[i] for i in idxs]

            # ← FIXED: initialize loss_r as Tensor →
            loss_r = torch.tensor(0.0, device=device)
            r_in = []

            for tr in batch:
                uns = [t for t in range(1, tr.durs.shape[0]+1) if t not in tr.curr_partialw]
                if not uns:
                    continue

                g = build_hetgraph(
                    tr.curr_g, tr.durs.shape[0], num_robots,
                    tr.durs.astype(np.float32), 6,
                    np.array(tr.locs), 1,
                    tr.curr_partials, np.array(uns),
                    rid, np.array(uns)
                ).to(device)

                try:
                    feat = hetgraph_node_helper(
                        g.number_of_nodes(),
                        tr.curr_partialw,
                        tr.curr_partials,
                        tr.locs,
                        tr.durs,
                        6,
                        num_robots,
                        len(uns)
                    )
                    feat = {k: torch.tensor(v, device=device, dtype=torch.float32)
                            for k, v in feat.items()}
                except IndexError:
                    continue

                out = system.forward_with_communication(rid, g, feat, communication_rounds=3)
                q, conf = out['value'], out['confidence']

                raw = float(tr.reward_n)
                nr = (raw - rmin) / (rmax - rmin + 1e-8)
                nr = float(np.clip(nr, 0.0, 1.0))

                targ = torch.full((len(uns),1), nr-0.01, device=device)
                wt   = torch.full((len(uns),1),
                                  0.8/(len(uns)-1) if len(uns)>1 else 1.0,
                                  device=device)
                idx  = uns.index(tr.act_task) if tr.act_task in uns else 0
                targ[idx] = nr; wt[idx] = 1.0

                mse = F.mse_loss(q, targ, reduction='none')
                cl  = (mse * wt * conf).sum() / absz
                cr  = torch.mean((conf - 0.8)**2) * 0.05

                probs = F.softmax(q.squeeze(), dim=0)
                loads[rid] += 1

                bl, bs_ = _calculate_workload_balance_loss(tr, rid, probs, num_robots, device)
                bl *= bw * 2.0

                ems = _estimate_makespan_from_transition(tr, device)
                mp  = torch.clamp(ems - tgt, min=0.0) * pw * 2.0

                ent = -torch.sum(probs * torch.log(probs + 1e-8))
                exb = 0.05 * ent * (1.0 + 0.3 * (1 - (step % phase_len)/phase_len))

                batch_loss = cl + cr + bl + mp - exb
                loss_r = loss_r + batch_loss
                r_in.append(nr)
                ms_list.append(ems.item())
                bal_list.append(bs_)

            optimizers[rid].zero_grad()
            if not torch.isnan(loss_r) and not torch.isinf(loss_r):
                loss_r.backward()
                utils.clip_grad_norm_(net.parameters(), max_norm=2.0)
                optimizers[rid].step()
                schedulers[rid].step(loss_r.item())
                total_loss += loss_r.item()
                batch_rs    += r_in

        avg_r   = np.mean(batch_rs) if batch_rs else 0.0
        real_bal= np.std(loads)
        avg_ms  = np.mean(ms_list)   if ms_list   else 0.0
        avg_bl  = np.mean(bal_list)  if bal_list  else 0.0
        vio     = sum(1 for m in ms_list if m > tgt)
        vio_rt  = vio / len(ms_list) if ms_list   else 0.0

        metrics.add_solution(avg_ms, avg_bl)

        writer.add_scalar('Loss/Total', total_loss, step)
        writer.add_scalar('Reward/Avg', avg_r, step)
        writer.add_scalar('Makespan/Average', avg_ms, step)
        writer.add_scalar('Balance/LoadStd', real_bal, step)
        writer.add_scalar('Balance/WorkloadStd', avg_bl, step)
        writer.add_scalar('Makespan/ViolationRate', vio_rt, step)
        writer.add_scalar('Training/CurriculumPhase', phase, step)

        history['steps'].append(step)
        history['losses'].append(total_loss)
        history['rewards'].append(avg_r)
        history['makespans'].append(avg_ms)
        history['balances'].append(avg_bl)
        history['violation_rates'].append(vio_rt)
        history['phases'].append(phase)

        if step % args.checkpoint_interval == 0:
            os.makedirs(args.cpsave, exist_ok=True)
            ckpt = os.path.join(args.cpsave, f"checkpoint_{step:05d}.tar")
            torch.save({
                'step': step,
                'networks':   {f'r{i}': system.get_robot_network(i).state_dict()   for i in range(num_robots)},
                'optimizers': {f'r{i}': optimizers[i].state_dict()                 for i in range(num_robots)}
            }, ckpt)

    writer.close()
    metrics.plot_pareto_front(os.path.join(args.tbdir, "pareto_front.png"), title="Pareto Front")
    _plot_training_curves(history, args.tbdir)
    _plot_training_summary(history, args.tbdir)
    print("✅ Training complete.")
    return system


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--path-to-train",      default="./problem_instances/constraints")
    parser.add_argument("--num-robots",         type=int,   default=2)
    parser.add_argument("--train-start-no",     type=int,   default=1)
    parser.add_argument("--train-end-no",       type=int,   default=100)
    parser.add_argument("--steps",              type=int,   default=1000)
    parser.add_argument("--batch-size",         type=int,   default=32)
    parser.add_argument("--lr",                 type=float, default=1e-5)
    parser.add_argument("--weight-decay",       type=float, default=5e-6)
    parser.add_argument("--alpha",              type=float, default=0.4)
    parser.add_argument("--beta",               type=float, default=0.6)
    parser.add_argument("--checkpoint-interval",type=int,   default=50)
    parser.add_argument("--cpsave",             default="./cp_decentralized")
    parser.add_argument("--tbdir",              default="./runs/DecentralizedMO")
    parser.add_argument("--device",             default="cpu")
    args = parser.parse_args()

    print(f"Decentralized training with α={args.alpha}, β={args.beta}")
    memories = collect_decentralized_data(
        args.path_to_train,
        args.train_start_no,
        args.train_end_no,
        args.num_robots,
        args.alpha,
        args.beta
    )
    if sum(len(m) for m in memories) == 0:
        print("No data found. Exiting.")
        return
    train_decentralized_system(memories, args.num_robots, args)


if __name__ == "__main__":
    main()
