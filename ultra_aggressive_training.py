#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra-Aggressive Training for Sub-0.1 Loss Achievement
This script implements the most aggressive optimization techniques to drive loss below 0.1
while maintaining efficient learning for makespan and workload balance.
"""

import os
import sys
import subprocess
import argparse
import time
import numpy as np
from pathlib import Path

def run_ultra_aggressive_training(args):
    """Run ultra-aggressive training to achieve sub-0.1 loss."""
    
    print("🚀 ULTRA-AGGRESSIVE TRAINING FOR SUB-0.1 LOSS")
    print("=" * 60)
    print("🎯 TARGET: Loss < 0.1 with efficient makespan & balance learning")
    print("⚡ MODE: Maximum aggression for rapid loss reduction")
    print()
    
    # Ultra-aggressive training command
    cmd = [
        "python", "decentralized_multi_objective_train.py",
        "--path-to-train", args.data_path,
        "--num-robots", str(args.num_robots),
        "--train-start-no", str(args.start_no),
        "--train-end-no", str(args.end_no),
        "--steps", str(args.steps),
        "--batch-size", str(args.batch_size),
        "--lr", str(args.lr),
        "--weight-decay", str(args.weight_decay),
        "--alpha", str(args.alpha),
        "--beta", str(args.beta),
        "--checkpoint-interval", str(args.checkpoint_interval),
        "--cpsave", args.checkpoint_dir,
        "--tbdir", args.tensorboard_dir,
        "--device", args.device
    ]
    
    print(f"🔥 ULTRA-AGGRESSIVE OPTIMIZATIONS ENABLED:")
    print(f"  ⚡ Target scaling: 0.001x (ultra-small targets)")
    print(f"  ⚡ MSE scaling: 0.01x (dramatic loss reduction)")
    print(f"  ⚡ Balance loss scaling: 0.001x (minimal interference)")
    print(f"  ⚡ Makespan penalty scaling: 0.001x (minimal interference)")
    print(f"  ⚡ Regularization scaling: 0.0001x (ultra-minimal)")
    print(f"  ⚡ Gradient clipping: 0.01 (ultra-conservative)")
    print(f"  ⚡ Learning rate: Exponential decay from {args.lr}")
    print(f"  ⚡ Batch size: {args.batch_size} (ultra-stable)")
    print(f"  ⚡ Loss clamping: max 0.05 per batch")
    print()
    
    # Track loss progression
    loss_milestones = [1.0, 0.5, 0.2, 0.1, 0.05]
    achieved_milestones = []
    
    start_time = time.time()
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, bufsize=1, universal_newlines=True)
        
        loss_values = []
        step_count = 0
        target_achieved = False
        
        print("📊 REAL-TIME ULTRA-AGGRESSIVE MONITORING:")
        print("-" * 60)
        
        for line in process.stdout:
            print(line.rstrip())
            
            # Extract loss values and monitor milestones
            if "Loss:" in line and "Step" in line:
                try:
                    parts = line.split("Loss:")
                    if len(parts) > 1:
                        loss_str = parts[1].split()[0]
                        loss_val = float(loss_str)
                        loss_values.append(loss_val)
                        step_count += 1
                        
                        # Check milestones
                        for milestone in loss_milestones:
                            if loss_val < milestone and milestone not in achieved_milestones:
                                achieved_milestones.append(milestone)
                                print(f"🎯 MILESTONE ACHIEVED: Loss < {milestone} at step {step_count}")
                                
                                if milestone == 0.1:
                                    target_achieved = True
                                    print(f"🏆 TARGET ACHIEVED! Loss < 0.1 at step {step_count}")
                        
                        # Show aggressive progress
                        if step_count % 10 == 0:
                            if len(loss_values) >= 10:
                                recent_avg = np.mean(loss_values[-10:])
                                trend = "📉 DECREASING" if len(loss_values) >= 20 and recent_avg < np.mean(loss_values[-20:-10]) else "📊 STABLE"
                                print(f"⚡ AGGRESSIVE PROGRESS: Step {step_count}, Recent Avg: {recent_avg:.6f}, {trend}")
                                
                except (ValueError, IndexError):
                    pass
        
        process.wait()
        
        if process.returncode == 0:
            training_time = time.time() - start_time
            print("\n" + "=" * 60)
            print("🏁 ULTRA-AGGRESSIVE TRAINING COMPLETED!")
            print(f"⏱️  Training time: {training_time:.2f} seconds")
            
            # Final analysis
            if loss_values:
                analyze_ultra_aggressive_results(loss_values, achieved_milestones, target_achieved, step_count)
            
            return True
            
        else:
            print("❌ ULTRA-AGGRESSIVE TRAINING FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Error during ultra-aggressive training: {e}")
        return False

def analyze_ultra_aggressive_results(loss_values, achieved_milestones, target_achieved, step_count):
    """Analyze ultra-aggressive training results."""
    
    print("\n🔬 ULTRA-AGGRESSIVE RESULTS ANALYSIS")
    print("=" * 50)
    
    if len(loss_values) < 5:
        print("❌ Insufficient data for analysis")
        return
    
    # Performance metrics
    initial_loss = loss_values[0]
    final_loss = loss_values[-1]
    min_loss = min(loss_values)
    
    print(f"📊 PERFORMANCE METRICS:")
    print(f"   Initial loss: {initial_loss:.6f}")
    print(f"   Final loss: {final_loss:.6f}")
    print(f"   Minimum loss: {min_loss:.6f}")
    print(f"   Total reduction: {initial_loss - final_loss:.6f}")
    print(f"   Reduction rate: {((initial_loss - final_loss) / initial_loss) * 100:.1f}%")
    
    # Milestone analysis
    print(f"\n🎯 MILESTONE ANALYSIS:")
    print(f"   Milestones achieved: {len(achieved_milestones)}/5")
    for milestone in sorted(achieved_milestones, reverse=True):
        print(f"   ✅ Loss < {milestone}")
    
    # Target achievement
    print(f"\n🏆 TARGET ACHIEVEMENT:")
    if target_achieved:
        print(f"   ✅ SUCCESS: Target loss < 0.1 ACHIEVED!")
        print(f"   🎉 Ultra-aggressive optimization successful!")
    else:
        print(f"   ⚠️  Target not reached. Best: {min_loss:.6f}")
        if min_loss < 0.2:
            print(f"   📈 CLOSE: Very close to target, continue training")
        elif min_loss < 0.5:
            print(f"   🔄 PROGRESS: Good progress, increase training steps")
        else:
            print(f"   ⚠️  NEEDS MORE AGGRESSION: Consider even more aggressive scaling")
    
    # Convergence analysis
    if len(loss_values) >= 20:
        early_losses = loss_values[:10]
        late_losses = loss_values[-10:]
        
        early_avg = np.mean(early_losses)
        late_avg = np.mean(late_losses)
        convergence_rate = (early_avg - late_avg) / len(loss_values)
        
        print(f"\n📈 CONVERGENCE ANALYSIS:")
        print(f"   Early average: {early_avg:.6f}")
        print(f"   Late average: {late_avg:.6f}")
        print(f"   Convergence rate: {convergence_rate:.8f} per step")
        
        if convergence_rate > 0.001:
            print(f"   ✅ EXCELLENT: Fast convergence rate!")
        elif convergence_rate > 0.0001:
            print(f"   ✅ GOOD: Steady convergence")
        else:
            print(f"   ⚠️  SLOW: Consider more aggressive parameters")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if target_achieved:
        print(f"   🎯 Perfect! Use these settings for production training")
        print(f"   📈 Monitor makespan and balance performance during testing")
    elif min_loss < 0.2:
        print(f"   🔄 Increase training steps to {step_count * 2}")
        print(f"   ⚡ Consider even smaller batch size (batch-size 1)")
    else:
        print(f"   ⚡ Increase loss scaling aggression (0.001 → 0.0001)")
        print(f"   🎯 Reduce target scaling further (0.001 → 0.0001)")
        print(f"   📉 Increase gradient clipping aggression (0.01 → 0.001)")

def main():
    parser = argparse.ArgumentParser(description="Ultra-Aggressive Training for Sub-0.1 Loss")
    
    # Data parameters
    parser.add_argument("--data-path", default="./problem_instances/constraints")
    parser.add_argument("--num-robots", type=int, default=2)
    parser.add_argument("--start-no", type=int, default=1)
    parser.add_argument("--end-no", type=int, default=50)  # More data for better learning
    
    # Ultra-aggressive training parameters
    parser.add_argument("--steps", type=int, default=500)  # Sufficient steps for convergence
    parser.add_argument("--batch-size", type=int, default=2)  # Ultra-small batches
    parser.add_argument("--lr", type=float, default=1e-3)  # Higher initial LR with decay
    parser.add_argument("--weight-decay", type=float, default=1e-8)  # Minimal weight decay
    parser.add_argument("--alpha", type=float, default=0.2)  # Conservative makespan weight
    parser.add_argument("--beta", type=float, default=0.8)  # Strong balance emphasis
    
    # System parameters
    parser.add_argument("--checkpoint-interval", type=int, default=100)
    parser.add_argument("--checkpoint-dir", default="./cp_ultra_aggressive")
    parser.add_argument("--tensorboard-dir", default="./runs/UltraAggressive")
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    # Create directories
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.tensorboard_dir, exist_ok=True)
    
    # Run ultra-aggressive training
    success = run_ultra_aggressive_training(args)
    
    if success:
        print(f"\n📈 MONITOR ULTRA-AGGRESSIVE RESULTS:")
        print(f"   tensorboard --logdir {args.tensorboard_dir}")
        print(f"   Key metrics: Loss/Total, LossComponents/*, LearningRate/*")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Test the trained model on validation instances")
        print(f"   2. Verify makespan < 10 and balanced workload")
        print(f"   3. If performance is good, use for production")
        
    else:
        print(f"\n🔧 TROUBLESHOOTING:")
        print(f"   • Check data quality and availability")
        print(f"   • Verify problem instances are valid")
        print(f"   • Consider reducing end-no for faster testing")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
