#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Training Issues Diagnostic Tool
Analyzes your training logs and provides specific recommendations.
"""

import re
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_training_log(log_text):
    """Analyze training log to identify issues."""
    
    print("🔍 TRAINING LOG ANALYSIS")
    print("=" * 50)
    
    # Extract loss values
    loss_pattern = r"Loss:\s*([\d.]+)"
    losses = [float(m) for m in re.findall(loss_pattern, log_text)]
    
    # Extract makespan values
    makespan_pattern = r"Makespan:\s*([\d.]+)"
    makespans = [float(m) for m in re.findall(makespan_pattern, log_text)]
    
    # Extract balance values
    balance_pattern = r"Balance:\s*([\d.]+)"
    balances = [float(m) for m in re.findall(balance_pattern, log_text)]
    
    print(f"📊 EXTRACTED DATA:")
    print(f"   Loss values: {len(losses)}")
    print(f"   Makespan values: {len(makespans)}")
    print(f"   Balance values: {len(balances)}")
    
    if not losses:
        print("❌ No loss values found in log")
        return
    
    # Analyze loss behavior
    print(f"\n📈 LOSS ANALYSIS:")
    print(f"   Initial loss: {losses[0]:.6f}")
    print(f"   Final loss: {losses[-1]:.6f}")
    print(f"   Min loss: {min(losses):.6f}")
    print(f"   Max loss: {max(losses):.6f}")
    print(f"   Loss range: {max(losses) - min(losses):.6f}")
    
    # Check for fluctuations
    if len(losses) > 10:
        loss_diff = np.diff(losses)
        increases = np.sum(loss_diff > 0)
        decreases = np.sum(loss_diff < 0)
        large_jumps = np.sum(np.abs(loss_diff) > 0.1)
        
        print(f"   Increases: {increases}")
        print(f"   Decreases: {decreases}")
        print(f"   Large jumps (>0.1): {large_jumps}")
        
        if large_jumps > len(losses) * 0.1:
            print("   ⚠️  HIGH FLUCTUATION detected!")
        elif decreases > increases:
            print("   ✅ Generally decreasing trend")
        else:
            print("   ⚠️  Unstable training")
    
    # Analyze makespan performance
    if makespans:
        print(f"\n🎯 MAKESPAN ANALYSIS:")
        print(f"   Training makespan: {makespans[-1]:.2f}")
        print(f"   Target: < 10.0")
        
        if makespans[-1] > 15:
            print("   ❌ POOR: Makespan too high")
        elif makespans[-1] > 10:
            print("   ⚠️  NEEDS IMPROVEMENT")
        else:
            print("   ✅ GOOD: Within target")
    
    # Analyze balance performance
    if balances:
        print(f"\n⚖️  BALANCE ANALYSIS:")
        print(f"   Training balance: {balances[-1]:.2f}")
        print(f"   Target: < 1.0")
        
        if balances[-1] > 2.0:
            print("   ❌ POOR: Balance too high")
        elif balances[-1] > 1.0:
            print("   ⚠️  NEEDS IMPROVEMENT")
        else:
            print("   ✅ GOOD: Within target")
    
    return losses, makespans, balances

def diagnose_issues(losses, makespans, balances):
    """Diagnose specific training issues and provide solutions."""
    
    print(f"\n🔬 ISSUE DIAGNOSIS")
    print("=" * 40)
    
    issues = []
    solutions = []
    
    # Check loss issues
    if losses:
        if min(losses) > 1.0:
            issues.append("Loss values too high (>1.0)")
            solutions.append("Reduce target scaling or increase learning rate")
        
        if max(losses) - min(losses) > 2.0:
            issues.append("High loss fluctuations")
            solutions.append("Remove curriculum learning, reduce learning rate")
        
        if len(losses) > 10:
            recent_trend = np.mean(losses[-5:]) - np.mean(losses[-10:-5])
            if recent_trend > 0.1:
                issues.append("Loss increasing instead of decreasing")
                solutions.append("Check for overfitting, add regularization")
    
    # Check makespan issues
    if makespans and makespans[-1] > 15:
        issues.append(f"Makespan too high ({makespans[-1]:.1f} > 10)")
        solutions.append("Increase makespan penalty weight, check reward calculation")
    
    # Check balance issues
    if balances and balances[-1] > 2.0:
        issues.append(f"Balance too poor ({balances[-1]:.1f} > 1.0)")
        solutions.append("Increase balance weight, check workload distribution")
    
    # Report issues
    if issues:
        print("❌ IDENTIFIED ISSUES:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n💡 RECOMMENDED SOLUTIONS:")
        for i, solution in enumerate(solutions, 1):
            print(f"   {i}. {solution}")
    else:
        print("✅ No major issues detected")
    
    return issues, solutions

def provide_specific_recommendations():
    """Provide specific recommendations based on common issues."""
    
    print(f"\n🎯 SPECIFIC RECOMMENDATIONS")
    print("=" * 40)
    
    print("🔧 FOR HIGH LOSS (>1.0):")
    print("   • Use moderate target scaling (0.1-0.5, not 0.001)")
    print("   • Remove aggressive loss scaling")
    print("   • Increase learning rate to 1e-4 or 5e-4")
    print("   • Use larger batch sizes (4-8)")
    
    print(f"\n🔧 FOR FLUCTUATING LOSS:")
    print("   • Remove curriculum learning completely")
    print("   • Use fixed parameters throughout training")
    print("   • Reduce learning rate")
    print("   • Increase gradient clipping")
    
    print(f"\n🔧 FOR TRAINING/VALIDATION MISMATCH:")
    print("   • Add stronger regularization (weight_decay=1e-4)")
    print("   • Use dropout or other generalization techniques")
    print("   • Increase training data diversity")
    print("   • Validate more frequently")
    
    print(f"\n🔧 FOR POOR MAKESPAN (>15):")
    print("   • Increase makespan penalty weight (alpha=0.7-0.8)")
    print("   • Check reward calculation for makespan")
    print("   • Ensure makespan is properly calculated in environment")
    print("   • Add explicit makespan loss term")
    
    print(f"\n🔧 FOR POOR BALANCE (>2.0):")
    print("   • Increase balance weight (beta=0.6-0.8)")
    print("   • Check workload distribution calculation")
    print("   • Add explicit balance regularization")
    print("   • Monitor robot task assignments")

def main():
    print("🔍 TRAINING ISSUES DIAGNOSTIC TOOL")
    print("=" * 50)
    print("This tool helps diagnose training issues from your logs.")
    print()
    
    # Check for recent log files
    log_files = list(Path(".").glob("*.log"))
    if log_files:
        print(f"📁 Found log files: {[f.name for f in log_files]}")
        log_file = log_files[0]
        print(f"📖 Analyzing: {log_file}")
        
        with open(log_file, 'r') as f:
            log_text = f.read()
    else:
        print("📝 No log files found. Please paste your training log:")
        print("(Paste log text and press Ctrl+D when done)")
        log_text = sys.stdin.read()
    
    # Analyze the log
    result = analyze_training_log(log_text)
    if result:
        losses, makespans, balances = result
        diagnose_issues(losses, makespans, balances)
    
    # Provide general recommendations
    provide_specific_recommendations()
    
    print(f"\n🚀 IMMEDIATE ACTION PLAN:")
    print("1. Try the balanced training script:")
    print("   python balanced_training_fix.py --steps 100 --end-no 20")
    print("2. Monitor validation performance closely")
    print("3. Adjust weights based on which metric needs improvement")
    print("4. Use moderate scaling, not ultra-aggressive")

if __name__ == "__main__":
    import sys
    main()
